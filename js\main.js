jQuery(document).ready(function($) {
    // Content Protection
    if ($('body').hasClass('protected-content')) {
        document.addEventListener('contextmenu', function(e) {
            e.preventDefault();
        });

        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey && 
                (e.keyCode === 67 || // C key
                 e.keyCode === 86 || // V key
                 e.keyCode === 85 || // U key
                 e.keyCode === 117)) { // F6 key
                return false;
            }
        });

        document.addEventListener('selectstart', function(e) {
            e.preventDefault();
        });
    }

    // Infinite scroll for novel grid
    if ($('.novel-grid').length) {
        var loading = false;
        var page = 1;

        $(window).scroll(function() {
            if ($(window).scrollTop() + $(window).height() >= $(document).height() - 100) {
                if (!loading) {
                    loading = true;
                    page++;

                    $.ajax({
                        url: ajax_object.ajax_url,
                        type: 'POST',
                        data: {
                            action: 'load_more_novels',
                            page: page,
                            nonce: ajax_object.nonce
                        },
                        success: function(response) {
                            if (response.success && response.data.html) {
                                $('.novel-grid').append(response.data.html);
                                loading = false;
                            }
                        }
                    });
                }
            }
        });
    }

    // Reading Progress Bar
    if ($('.chapter-content').length) {
        var progressBar = $('<div/>', {
            class: 'reading-progress'
        }).css({
            'position': 'fixed',
            'top': 0,
            'left': 0,
            'width': '0%',
            'height': '3px',
            'background': '#007bff',
            'z-index': 9999,
            'transition': 'width 0.2s ease-in-out'
        }).appendTo('body');

        $(window).scroll(function() {
            var scrollPercent = 100 * $(window).scrollTop() / 
                ($(document).height() - $(window).height());
            progressBar.css('width', scrollPercent + '%');
        });
    }

    // Theme Switcher - Updated to use data-theme attribute system with dark as default
    var currentTheme = localStorage.getItem('reading-theme') || localStorage.getItem('theme') || 'dark';

    // Apply initial theme
    $('body').attr('data-theme', currentTheme);
    $('html').attr('data-theme', currentTheme);

    $('#theme-switcher').click(function() {
        currentTheme = currentTheme === 'dark' ? 'light' : 'dark';

        // Remove old class-based theme system
        $('body').removeClass('theme-light theme-dark');

        // Apply new data-theme attribute system
        $('body').attr('data-theme', currentTheme);
        $('html').attr('data-theme', currentTheme);

        // Save to both storage keys for compatibility
        localStorage.setItem('theme', currentTheme);
        localStorage.setItem('reading-theme', currentTheme);

        // Trigger theme change event
        $(document).trigger('themeChanged', { theme: currentTheme });
    });

    // Search Autocomplete
    var searchTimeout;
    $('#novel-search').on('input', function() {
        clearTimeout(searchTimeout);
        var query = $(this).val();

        if (query.length >= 2) {
            searchTimeout = setTimeout(function() {
                $.ajax({
                    url: ajax_object.ajax_url,
                    type: 'POST',
                    data: {
                        action: 'search_novels',
                        query: query,
                        nonce: ajax_object.nonce
                    },
                    success: function(response) {
                        if (response.success) {
                            var results = response.data;
                            var resultsHtml = '';

                            results.forEach(function(novel) {
                                resultsHtml += `
                                    <div class="search-result">
                                        <a href="${novel.url}">
                                            <img src="${novel.thumbnail}" alt="${novel.title}">
                                            <div class="result-info">
                                                <h4>${novel.title}</h4>
                                                <p>${novel.author}</p>
                                            </div>
                                        </a>
                                    </div>
                                `;
                            });

                            $('#search-results').html(resultsHtml).show();
                        }
                    }
                });
            }, 300);
        } else {
            $('#search-results').hide();
        }
    });

    // Close search results when clicking outside
    $(document).click(function(e) {
        if (!$(e.target).closest('.search-container').length) {
            $('#search-results').hide();
        }
    });

    // Reading Time Calculation
    if ($('.chapter-content').length) {
        var text = $('.chapter-text').text();
        var wordCount = text.trim().split(/\s+/).length;
        var readingTime = Math.ceil(wordCount / 200); // Assuming 200 words per minute
        $('.reading-time').text(readingTime + ' min read');
    }
});
