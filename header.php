<!DOCTYPE html>
<html <?php language_attributes(); ?>>
<head>
    <meta charset="<?php bloginfo('charset'); ?>">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="theme-color" content="#343a40">
    <meta name="msapplication-TileColor" content="#343a40">

    <!-- SEO Meta Tags -->
    <?php if (is_single() && get_post_type() == 'novel') : ?>
        <meta name="description" content="<?php echo esc_attr(wp_trim_words(get_the_excerpt(), 25)); ?>">
        <meta property="og:type" content="book">
        <meta property="og:title" content="<?php echo esc_attr(get_the_title()); ?>">
        <meta property="og:description" content="<?php echo esc_attr(wp_trim_words(get_the_excerpt(), 25)); ?>">
        <meta property="og:url" content="<?php echo esc_url(get_permalink()); ?>">
        <?php if (has_post_thumbnail()) : ?>
            <meta property="og:image" content="<?php echo esc_url(get_the_post_thumbnail_url(get_the_ID(), 'large')); ?>">
        <?php endif; ?>
        <meta property="book:author" content="<?php echo esc_attr(get_post_meta(get_the_ID(), '_author', true) ?: get_post_meta(get_the_ID(), '_novel_author', true)); ?>">
        <meta property="book:genre" content="Light Novel">
    <?php elseif (is_single() && get_post_type() == 'post') : ?>
        <?php
        $novel_id = get_post_meta(get_the_ID(), '_novel_id', true);
        $novel = $novel_id ? get_post($novel_id) : null;
        $chapter_number = get_post_meta(get_the_ID(), '_chapter_number', true);
        ?>
        <meta name="description" content="Read <?php echo $novel ? esc_attr($novel->post_title) : 'this novel'; ?> Chapter <?php echo esc_attr($chapter_number); ?> online for free at <?php echo esc_attr(get_bloginfo('name')); ?>.">
        <meta property="og:type" content="article">
        <meta property="og:title" content="<?php echo esc_attr(get_the_title()); ?>">
        <meta property="og:description" content="Read <?php echo $novel ? esc_attr($novel->post_title) : 'this novel'; ?> Chapter <?php echo esc_attr($chapter_number); ?> online for free.">
        <meta property="og:url" content="<?php echo esc_url(get_permalink()); ?>">
        <meta property="article:author" content="<?php echo esc_attr(get_the_author()); ?>">
        <meta property="article:published_time" content="<?php echo esc_attr(get_the_date('c')); ?>">
        <meta property="article:modified_time" content="<?php echo esc_attr(get_the_modified_date('c')); ?>">
    <?php else : ?>
        <meta name="description" content="<?php echo esc_attr(get_bloginfo('description')); ?>">
        <meta property="og:type" content="website">
        <meta property="og:title" content="<?php echo esc_attr(wp_get_document_title()); ?>">
        <meta property="og:description" content="<?php echo esc_attr(get_bloginfo('description')); ?>">
        <meta property="og:url" content="<?php echo esc_url(home_url()); ?>">
    <?php endif; ?>

    <!-- Twitter Card -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="<?php echo esc_attr(wp_get_document_title()); ?>">
    <meta name="twitter:description" content="<?php echo esc_attr(get_bloginfo('description')); ?>">
    <?php if (has_post_thumbnail() && (is_single() || is_page())) : ?>
        <meta name="twitter:image" content="<?php echo esc_url(get_the_post_thumbnail_url(get_the_ID(), 'large')); ?>">
    <?php endif; ?>

    <!-- Additional SEO Meta Tags -->
    <meta name="robots" content="index, follow, max-image-preview:large, max-snippet:-1, max-video-preview:-1">
    <meta name="googlebot" content="index, follow">
    <?php if (is_single() && get_post_type() == 'novel') : ?>
        <meta name="article:section" content="Light Novels">
        <meta name="article:tag" content="<?php echo esc_attr(implode(', ', wp_get_post_tags(get_the_ID(), array('fields' => 'names')))); ?>">
    <?php endif; ?>

    <!-- Canonical URL -->
    <link rel="canonical" href="<?php echo esc_url(get_permalink()); ?>"><?php if (is_home() || is_front_page()) : ?>
    <link rel="canonical" href="<?php echo esc_url(home_url('/')); ?>"><?php endif; ?>

    <!-- Preconnect for performance -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link rel="preconnect" href="https://cdn.jsdelivr.net">

    <?php wp_head(); ?>
</head>
<body <?php body_class(); ?>>
    <?php wp_body_open(); ?>

    <!-- Skip to content link for accessibility -->
    <a class="skip-link screen-reader-text" href="#main"><?php esc_html_e('Skip to content', 'ln-reader'); ?></a>

    <header id="masthead" class="site-header" role="banner">
        <nav class="navbar navbar-expand-lg navbar-dark bg-dark" role="navigation" aria-label="<?php esc_attr_e('Primary Navigation', 'ln-reader'); ?>">
            <div class="container">
                <?php
                if (has_custom_logo()) {
                    the_custom_logo();
                } else {
                    echo '<a class="navbar-brand" href="' . esc_url(home_url('/')) . '" rel="home">' . get_bloginfo('name') . '</a>';
                }
                ?>

                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarMain" aria-controls="navbarMain" aria-expanded="false" aria-label="<?php esc_attr_e('Toggle navigation', 'ln-reader'); ?>">
                    <span class="navbar-toggler-icon"></span>
                </button>

                <div class="collapse navbar-collapse" id="navbarMain">
                    <?php
                    wp_nav_menu(array(
                        'theme_location'  => 'primary',
                        'depth'           => 2,
                        'container'       => false,
                        'menu_class'      => 'navbar-nav ms-auto mb-2 mb-lg-0',
                        'fallback_cb'     => 'WP_Bootstrap_Navwalker::fallback',
                        'walker'          => new WP_Bootstrap_Navwalker()
                    ));
                    ?>

                    <!-- User Authentication Menu -->
                    <div class="navbar-nav ms-lg-3">
                        <?php if (is_user_logged_in()) :
                            $current_user = wp_get_current_user(); ?>
                            <div class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false" aria-label="<?php esc_attr_e('User menu', 'ln-reader'); ?>">
                                    <i class="fas fa-user" aria-hidden="true"></i>
                                    <span class="user-name"><?php echo esc_html($current_user->display_name); ?></span>
                                </a>
                                <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                                    <li><a class="dropdown-item" href="<?php echo esc_url(home_url('/dashboard')); ?>">
                                        <i class="fas fa-tachometer-alt" aria-hidden="true"></i> <?php esc_html_e('Dashboard', 'ln-reader'); ?>
                                    </a></li>
                                    <li><a class="dropdown-item" href="<?php echo esc_url(home_url('/bookmarks')); ?>">
                                        <i class="fas fa-bookmark" aria-hidden="true"></i> <?php esc_html_e('Bookmarks', 'ln-reader'); ?>
                                    </a></li>
                                    <?php if (current_user_can('administrator')) : ?>
                                        <li><a class="dropdown-item" href="<?php echo esc_url(admin_url()); ?>">
                                            <i class="fas fa-cog" aria-hidden="true"></i> <?php esc_html_e('Admin Panel', 'ln-reader'); ?>
                                        </a></li>
                                    <?php endif; ?>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="<?php echo esc_url(wp_logout_url()); ?>">
                                        <i class="fas fa-sign-out-alt" aria-hidden="true"></i> <?php esc_html_e('Logout', 'ln-reader'); ?>
                                    </a></li>
                                </ul>
                            </div>
                        <?php else : ?>
                            <a class="nav-link" href="<?php echo esc_url(home_url('/login')); ?>">
                                <i class="fas fa-sign-in-alt" aria-hidden="true"></i> <?php esc_html_e('Login', 'ln-reader'); ?>
                            </a>
                            <a class="nav-link" href="<?php echo esc_url(home_url('/register')); ?>">
                                <i class="fas fa-user-plus" aria-hidden="true"></i> <?php esc_html_e('Register', 'ln-reader'); ?>
                            </a>
                        <?php endif; ?>
                    </div>

                    <form class="d-flex ms-lg-3" role="search" method="get" action="<?php echo esc_url(home_url('/')); ?>" aria-label="<?php esc_attr_e('Search novels', 'ln-reader'); ?>">
                        <div class="input-group">
                            <label for="novel-search" class="screen-reader-text"><?php esc_html_e('Search novels', 'ln-reader'); ?></label>
                            <input id="novel-search" class="form-control form-control-sm" type="search" placeholder="<?php esc_attr_e('Search novels...', 'ln-reader'); ?>" name="s" value="<?php echo esc_attr(get_search_query()); ?>">
                            <input type="hidden" name="post_type" value="novel">
                            <button class="btn btn-outline-light btn-sm" type="submit" aria-label="<?php esc_attr_e('Submit search', 'ln-reader'); ?>">
                                <i class="fas fa-search" aria-hidden="true"></i>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </nav>

        <!-- Header Ad Placement -->
        <?php if (is_active_sidebar('header-ads')) : ?>
            <div class="header-ads" role="complementary" aria-label="<?php esc_attr_e('Header advertisements', 'ln-reader'); ?>">
                <div class="container">
                    <?php dynamic_sidebar('header-ads'); ?>
                </div>
            </div>
        <?php endif; ?>
    </header>

    <main id="main" class="site-main" role="main">
        <div class="container my-4">
