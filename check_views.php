<?php
require_once '../../../wp-config.php';
require_once '../../../wp-load.php';

// Check Seijo novel (ID: 471) that we just visited
$novel_id = 471;
echo "Checking Seijo novel (ID: $novel_id) view count:\n";

$views = get_post_meta($novel_id, '_novel_views', true);
echo "Current views: " . $views . "\n";

// Check all novels to see current state
$novels = get_posts([
    'post_type' => 'novel',
    'posts_per_page' => 5,
    'post_status' => 'publish'
]);

echo "\nAll novel view counts:\n";
foreach ($novels as $novel) {
    $views = get_post_meta($novel->ID, '_novel_views', true);
    echo "ID " . $novel->ID . " (" . substr($novel->post_title, 0, 30) . "...): " . $views . " views\n";
}
?>
