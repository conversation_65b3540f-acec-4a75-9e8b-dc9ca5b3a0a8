/**
 * LN Reader Theme Enhancements
 * Enhanced user experience features
 */

(function($) {
    'use strict';

    // Theme initialization
    $(document).ready(function() {
        initializeTheme();
        initializeReadingSettings();
        initializeLazyLoading();
        initializeProgressTracking();
        initializeKeyboardNavigation();
        initializeCopyToClipboard();
        initializeScrollToTop();
    });

    // Theme initialization
    function initializeTheme() {
        // Apply saved theme preference - check both storage keys for compatibility, default to dark
        const savedTheme = localStorage.getItem('reading-theme') || localStorage.getItem('theme') || 'dark';
        applyTheme(savedTheme);

        // Apply saved font size
        const savedFontSize = localStorage.getItem('font-size') || 'medium';
        applyFontSize(savedFontSize);

        // Ensure theme is applied to all necessary elements on page load
        setTimeout(() => {
            removeInlineThemeStyles();
        }, 100);
    }

    // Reading settings
    function initializeReadingSettings() {
        // Theme toggle
        $('#theme-toggle').on('click', function() {
            const currentTheme = $('body').attr('data-theme') || 'dark';
            const newTheme = currentTheme === 'dark' ? 'light' : currentTheme === 'light' ? 'sepia' : 'dark';
            applyTheme(newTheme);
            localStorage.setItem('reading-theme', newTheme);
        });

        // Font size toggle
        $('#font-size-toggle').on('click', function() {
            const currentSize = $('body').attr('data-font-size') || 'medium';
            const sizes = ['small', 'medium', 'large', 'extra-large'];
            const currentIndex = sizes.indexOf(currentSize);
            const newSize = sizes[(currentIndex + 1) % sizes.length];
            applyFontSize(newSize);
            localStorage.setItem('font-size', newSize);
        });

        // Chapter sorting
        $('#sort-chapters').on('click', function() {
            const $btn = $(this);
            const currentSort = $btn.data('sort');
            const newSort = currentSort === 'asc' ? 'desc' : 'asc';
            
            $btn.data('sort', newSort);
            $btn.find('i').toggleClass('fa-sort-numeric-down fa-sort-numeric-up');
            
            sortChapters(newSort);
        });
    }

    // Apply theme
    function applyTheme(theme) {
        // Set data-theme attribute on body and html elements
        $('body').attr('data-theme', theme);
        $('html').attr('data-theme', theme);

        // Also set on main containers for better coverage
        $('.theme-content-wrapper, .container, .container-fluid').attr('data-theme', theme);

        // Update theme toggle icon
        const icons = {
            light: 'fas fa-sun',
            dark: 'fas fa-moon',
            sepia: 'fas fa-leaf'
        };

        $('#theme-toggle i').attr('class', icons[theme] || icons.dark);

        // Dispatch custom event for other components
        $(document).trigger('themeChanged', { theme: theme });

        // Remove any conflicting inline styles that might override CSS
        removeInlineThemeStyles();
    }

    // Remove inline styles that conflict with CSS theme system
    function removeInlineThemeStyles() {
        const elementsToClean = [
            '.theme-content-wrapper',
            '.container',
            '.chapter-content',
            '.chapter-nav',
            '.breadcrumb',
            '.chapter-text',
            '.modal-content',
            '.modal-header',
            '.modal-body',
            '.form-label',
            '.btn-secondary',
            '.btn-close',
            '.form-select',
            '.form-control',
            '.btn-outline-primary',
            'pre',
            'code',
            'blockquote'
        ];

        elementsToClean.forEach(selector => {
            $(selector).css({
                'background-color': '',
                'color': '',
                'border-color': '',
                'filter': ''
            });
        });
    }

    // Apply font size
    function applyFontSize(size) {
        $('body').attr('data-font-size', size);
        
        const sizes = {
            'small': '0.875rem',
            'medium': '1rem',
            'large': '1.125rem',
            'extra-large': '1.25rem'
        };
        
        $('.chapter-text').css('font-size', sizes[size] || sizes.medium);
    }

    // Sort chapters
    function sortChapters(order) {
        const $chapterList = $('.chapter-list');
        const $chapters = $chapterList.find('.chapter-item').get();
        
        $chapters.sort(function(a, b) {
            const aNum = parseInt($(a).find('.chapter-number').text().match(/\d+/)[0]);
            const bNum = parseInt($(b).find('.chapter-number').text().match(/\d+/)[0]);
            
            return order === 'asc' ? aNum - bNum : bNum - aNum;
        });
        
        $chapterList.empty().append($chapters);
    }

    // Lazy loading for images
    function initializeLazyLoading() {
        if ('IntersectionObserver' in window) {
            const imageObserver = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        img.src = img.dataset.src;
                        img.classList.remove('lazy');
                        imageObserver.unobserve(img);
                    }
                });
            });

            document.querySelectorAll('img[data-src]').forEach(img => {
                imageObserver.observe(img);
            });
        }
    }

    // Progress tracking
    function initializeProgressTracking() {
        if ($('.chapter-content').length) {
            let isTracking = false;
            
            $(window).on('scroll', function() {
                if (!isTracking) {
                    isTracking = true;
                    requestAnimationFrame(updateReadingProgress);
                }
            });
            
            function updateReadingProgress() {
                const $content = $('.chapter-content');
                const contentTop = $content.offset().top;
                const contentHeight = $content.outerHeight();
                const windowTop = $(window).scrollTop();
                const windowHeight = $(window).height();
                
                const progress = Math.min(100, Math.max(0, 
                    ((windowTop + windowHeight - contentTop) / contentHeight) * 100
                ));
                
                // Update progress indicator if exists
                $('.reading-progress').css('width', progress + '%');
                
                // Save progress for logged-in users
                if (progress > 80 && window.novelId && window.chapterId) {
                    saveReadingProgress(window.novelId, window.chapterId);
                }
                
                isTracking = false;
            }
        }
    }

    // Save reading progress
    function saveReadingProgress(novelId, chapterId) {
        if (window.ajaxurl) {
            $.post(window.ajaxurl, {
                action: 'save_reading_progress',
                novel_id: novelId,
                chapter_id: chapterId,
                nonce: window.progressNonce
            });
        }
    }

    // Keyboard navigation
    function initializeKeyboardNavigation() {
        $(document).on('keydown', function(e) {
            // Only on chapter pages
            if (!$('.chapter-content').length) return;
            
            switch(e.key) {
                case 'ArrowLeft':
                    e.preventDefault();
                    $('.chapter-nav-btn[href]:contains("Previous")').first().click();
                    break;
                case 'ArrowRight':
                    e.preventDefault();
                    $('.chapter-nav-btn[href]:contains("Next")').first().click();
                    break;
                case 'Escape':
                    e.preventDefault();
                    window.history.back();
                    break;
            }
        });
    }

    // Copy to clipboard
    function initializeCopyToClipboard() {
        $('#copy-url').on('click', function() {
            const url = window.location.href;
            
            if (navigator.clipboard) {
                navigator.clipboard.writeText(url).then(() => {
                    showToast('URL copied to clipboard!');
                });
            } else {
                // Fallback for older browsers
                const textArea = document.createElement('textarea');
                textArea.value = url;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                showToast('URL copied to clipboard!');
            }
        });
    }

    // Scroll to top
    function initializeScrollToTop() {
        const $scrollBtn = $('<button class="scroll-to-top" aria-label="Scroll to top"><i class="fas fa-chevron-up"></i></button>');
        $('body').append($scrollBtn);
        
        $(window).on('scroll', function() {
            if ($(window).scrollTop() > 300) {
                $scrollBtn.addClass('visible');
            } else {
                $scrollBtn.removeClass('visible');
            }
        });
        
        $scrollBtn.on('click', function() {
            $('html, body').animate({ scrollTop: 0 }, 300);
        });
    }

    // Toast notifications
    function showToast(message, type = 'success') {
        const $toast = $(`
            <div class="toast toast-${type}">
                <div class="toast-content">
                    <i class="fas fa-${type === 'success' ? 'check' : 'exclamation'}-circle"></i>
                    <span>${message}</span>
                </div>
            </div>
        `);
        
        $('body').append($toast);
        
        setTimeout(() => {
            $toast.addClass('show');
        }, 100);
        
        setTimeout(() => {
            $toast.removeClass('show');
            setTimeout(() => $toast.remove(), 300);
        }, 3000);
    }

    // Bookmark functionality enhancement
    $(document).on('click', '.toggle-bookmark', function(e) {
        e.preventDefault();
        const $btn = $(this);
        const $icon = $btn.find('i');
        const $text = $btn.find('.bookmark-text, span');
        
        $btn.prop('disabled', true);
        
        $.ajax({
            url: window.ajaxurl || '/wp-admin/admin-ajax.php',
            type: 'POST',
            data: {
                action: 'toggle_bookmark',
                novel_id: $btn.data('novel-id'),
                security: $btn.data('nonce')
            },
            success: function(response) {
                if (response.success) {
                    if (response.data.action === 'added') {
                        $icon.removeClass('far').addClass('fas');
                        $text.text('Bookmarked');
                        showToast('Added to bookmarks!');
                    } else {
                        $icon.removeClass('fas').addClass('far');
                        $text.text('Bookmark');
                        showToast('Removed from bookmarks!');
                    }
                } else {
                    showToast('Error updating bookmark', 'error');
                }
            },
            error: function() {
                showToast('Error updating bookmark', 'error');
            },
            complete: function() {
                $btn.prop('disabled', false);
            }
        });
    });

    // Rating functionality enhancement
    $(document).on('click', '.star-btn', function() {
        const rating = $(this).data('rating');
        const $stars = $('.star-btn');
        
        // Visual feedback
        $stars.each(function(index) {
            const $star = $(this);
            if (index < rating) {
                $star.find('i').removeClass('far').addClass('fas');
            } else {
                $star.find('i').removeClass('fas').addClass('far');
            }
        });
        
        // Save rating
        $.ajax({
            url: window.ajaxurl || '/wp-admin/admin-ajax.php',
            type: 'POST',
            data: {
                action: 'save_novel_rating',
                post_id: window.novelId || $('[data-novel-id]').data('novel-id'),
                rating: rating,
                nonce: window.ratingNonce
            },
            success: function(response) {
                if (response.success) {
                    showToast('Rating saved!');
                    setTimeout(() => location.reload(), 1000);
                } else {
                    showToast('Error saving rating', 'error');
                }
            },
            error: function() {
                showToast('Error saving rating', 'error');
            }
        });
    });

})(jQuery);
