/*
Theme Name: LN Reader
Theme URI: http://your-domain.com/ln-reader
Author: Your Name
Author URI: http://your-domain.com
Description: A WordPress theme for light novel reading website with Google OAuth integration and AdSense optimization
Version: 1.3.0
License: GNU General Public License v2 or later
License URI: http://www.gnu.org/licenses/gpl-2.0.html
Text Domain: ln-reader

Changelog:
v1.3.0 - AdSense Integration & Site Kit Compatibility
- Added comprehensive AdSense-friendly ad placement areas
- Implemented Google Site Kit and Auto Ads compatibility
- Created 11 strategic widget areas for ad placements
- Added responsive ad styling and mobile optimization
- Enhanced with Auto Ads detection and coordination
- Improved performance with minimal layout impact
- Added admin tools for AdSense configuration monitoring

v1.2.0 - Google OAuth Integration & Import Fixes
- Added Google Sign-In/Sign-Up functionality
- Enhanced authentication system security
- Fixed import warning display issues
- Improved admin interface for OAuth configuration
- Added comprehensive error handling and logging
- Enhanced CSS styling for social login buttons
- Added AJAX functionality for better UX

v1.1.0 - Base Theme
- Initial light novel reading theme
- Custom post types for novels and chapters
- Import functionality
- Basic authentication system
*/

/* Font Imports - Optimized for reading experience */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Merriweather:wght@300;400;700&display=swap');

/* CSS Custom Properties for theming */
:root {
    /* Colors */
    --primary-color: #1a73e8;
    --secondary-color: #34a853;
    --accent-color: #ea4335;
    --text-primary: #202124;
    --text-secondary: #5f6368;
    --text-muted: #80868b;
    --background-primary: #ffffff;
    --background-secondary: #f8f9fa;
    --background-tertiary: #e8eaed;
    --border-color: #dadce0;
    --border-light: #e8eaed;
    --shadow-light: 0 1px 3px rgba(60, 64, 67, 0.1);
    --shadow-medium: 0 2px 8px rgba(60, 64, 67, 0.15);
    --shadow-heavy: 0 4px 16px rgba(60, 64, 67, 0.2);

    /* Typography */
    --font-family-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-family-reading: 'Merriweather', Georgia, serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;

    /* Line Heights */
    --line-height-tight: 1.25;
    --line-height-normal: 1.5;
    --line-height-relaxed: 1.625;
    --line-height-loose: 2;

    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;

    /* Border Radius */
    --radius-sm: 0.25rem;
    --radius-md: 0.375rem;
    --radius-lg: 0.5rem;
    --radius-xl: 0.75rem;

    /* Transitions */
    --transition-fast: 0.15s ease-in-out;
    --transition-normal: 0.3s ease-in-out;
    --transition-slow: 0.5s ease-in-out;
}

/* Dark theme variables - also applied as default when no theme is set */
[data-theme="dark"],
body:not([data-theme]),
html:not([data-theme]) {
    --text-primary: #e8eaed;
    --text-secondary: #9aa0a6;
    --text-muted: #80868b;
    --background-primary: #202124;
    --background-secondary: #303134;
    --background-tertiary: #3c4043;
    --border-color: #5f6368;
    --border-light: #3c4043;

    /* Additional dark theme colors */
    --card-background: #1a1a1a;
    --content-background: #1a1a1a;
    --modal-background: #1a1a1a;
    --input-background: #1a1a1a;
    --navbar-background: #1a1a1a;
    --footer-background: #1a1a1a;
}

/* Sepia theme variables */
[data-theme="sepia"] {
    --text-primary: #5c4b37;
    --text-secondary: #8b7355;
    --text-muted: #a08b73;
    --background-primary: #f7f3e9;
    --background-secondary: #f0e6d2;
    --background-tertiary: #e8dcbf;
    --border-color: #d4c4a8;
    --border-light: #e8dcbf;
}

/* Base Styles */
* {
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-base);
    line-height: var(--line-height-normal);
    background: var(--background-secondary);
    color: var(--text-primary);
    margin: 0;
    padding: 0;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
}

/* Improved link styles */
a {
    color: var(--primary-color);
    text-decoration: none;
    transition: color var(--transition-fast);
}

a:hover,
a:focus {
    color: var(--accent-color);
    text-decoration: underline;
}

a:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* Enhanced heading hierarchy */
h1, .h1 {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-3xl);
    font-weight: 700;
    line-height: var(--line-height-tight);
    color: var(--text-primary);
    margin: 0 0 var(--spacing-lg) 0;
}

h2, .h2 {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-2xl);
    font-weight: 600;
    line-height: var(--line-height-tight);
    color: var(--text-primary);
    margin: var(--spacing-xl) 0 var(--spacing-md) 0;
}

h3, .h3 {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-xl);
    font-weight: 600;
    line-height: var(--line-height-tight);
    color: var(--text-primary);
    margin: var(--spacing-lg) 0 var(--spacing-sm) 0;
}

h4, .h4 {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-lg);
    font-weight: 500;
    line-height: var(--line-height-normal);
    color: var(--text-primary);
    margin: var(--spacing-md) 0 var(--spacing-sm) 0;
}

h5, .h5 {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-base);
    font-weight: 500;
    line-height: var(--line-height-normal);
    color: var(--text-secondary);
    margin: var(--spacing-md) 0 var(--spacing-xs) 0;
}

h6, .h6 {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-sm);
    font-weight: 500;
    line-height: var(--line-height-normal);
    color: var(--text-secondary);
    margin: var(--spacing-sm) 0 var(--spacing-xs) 0;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

/* Accessibility and Screen Reader Support */
.screen-reader-text {
    position: absolute !important;
    clip: rect(1px, 1px, 1px, 1px);
    width: 1px !important;
    height: 1px !important;
    overflow: hidden;
    word-wrap: normal !important;
}

.skip-link {
    position: absolute;
    left: -9999px;
    top: 0;
    z-index: 999999;
    padding: var(--spacing-sm) var(--spacing-md);
    background: var(--background-primary);
    color: var(--text-primary);
    text-decoration: none;
    border: 2px solid var(--primary-color);
    border-radius: var(--radius-md);
}

.skip-link:focus {
    left: var(--spacing-md);
    clip: auto !important;
    width: auto !important;
    height: auto !important;
}

/* Focus management */
*:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

button:focus,
input:focus,
select:focus,
textarea:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* Breadcrumb Navigation */
.breadcrumb-navigation {
    margin: var(--spacing-md) 0;
    padding: var(--spacing-sm) 0;
    border-bottom: 1px solid var(--border-light);
}

.breadcrumb {
    display: flex;
    flex-wrap: wrap;
    list-style: none;
    margin: 0;
    padding: 0;
    font-size: var(--font-size-sm);
}

.breadcrumb-item {
    display: flex;
    align-items: center;
}

.breadcrumb-item + .breadcrumb-item::before {
    content: "/";
    margin: 0 var(--spacing-sm);
    color: var(--text-muted);
}

.breadcrumb-item a {
    color: var(--primary-color);
    text-decoration: none;
    transition: color var(--transition-fast);
}

.breadcrumb-item a:hover {
    color: var(--accent-color);
    text-decoration: underline;
}

.breadcrumb-item.active {
    color: var(--text-secondary);
}

/* Header */
.site-header {
    position: sticky;
    top: 0;
    z-index: 1000;
    background: var(--background-primary);
    box-shadow: var(--shadow-light);
}

.navbar {
    background-color: var(--background-primary);
    border-bottom: 1px solid var(--border-color);
    padding: var(--spacing-sm) 0;
}

.navbar-brand {
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--text-primary);
    text-decoration: none;
}

.navbar-brand:hover,
.navbar-brand:focus {
    color: var(--primary-color);
}

.navbar-nav .nav-link {
    color: var(--text-secondary);
    font-weight: 500;
    padding: var(--spacing-sm) var(--spacing-md);
    transition: color var(--transition-fast);
}

.navbar-nav .nav-link:hover,
.navbar-nav .nav-link:focus {
    color: var(--primary-color);
}

.navbar-toggler {
    border: none;
    padding: var(--spacing-xs);
    background: transparent;
}

.navbar-toggler:focus {
    box-shadow: 0 0 0 2px var(--primary-color);
}

/* User menu improvements */
.user-name {
    margin-left: var(--spacing-xs);
}

.dropdown-menu {
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow-medium);
    border-radius: var(--radius-lg);
    padding: var(--spacing-sm) 0;
}

.dropdown-item {
    padding: var(--spacing-sm) var(--spacing-md);
    color: var(--text-primary);
    transition: background-color var(--transition-fast);
}

.dropdown-item:hover,
.dropdown-item:focus {
    background-color: var(--background-secondary);
    color: var(--text-primary);
}

.dropdown-item i {
    margin-right: var(--spacing-sm);
    width: 1rem;
    text-align: center;
}

/* Search form improvements */
.navbar form {
    position: relative;
}

.navbar .form-control {
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md) 0 0 var(--radius-md);
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: var(--font-size-sm);
    background: var(--background-primary);
    color: var(--text-primary);
}

.navbar .form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(26, 115, 232, 0.2);
}

.navbar .btn {
    border-radius: 0 var(--radius-md) var(--radius-md) 0;
    border: 1px solid var(--primary-color);
    background: var(--primary-color);
    color: white;
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: var(--font-size-sm);
}

.navbar .btn:hover,
.navbar .btn:focus {
    background: var(--accent-color);
    border-color: var(--accent-color);
}

/* Main Layout Improvements */
.site-content {
    padding: 2rem 0;
}

.container {
    max-width: 1500px !important;
    padding-right: 1rem;
    padding-left: 1rem;
    margin-right: auto;
    margin-left: auto;
}

/* Card Styling */
.card {
    border: none;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    background: #fff;
    margin-bottom: 1.5rem;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

/* Novel Grid */
.novel-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.novel-card {
    display: flex;
    gap: 1rem;
    padding: 1rem;
    background: #fff;
    border-bottom: 1px solid #edf2f7;
}

.novel-card:last-child {
    border-bottom: none;
}

.novel-cover {
    position: relative;
    width: 100%;
    max-width: 100%;
    margin: 0;
}

.novel-cover img {
    width: 100%;
    height: auto;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    display: block;
}

.novel-content {
    flex: 1;
    min-width: 0;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
}

.novel-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin: 0 0 0.5rem;
    color: #2c3e50;
}

.novel-status {
    position: absolute;
    top: 8px;
    right: 8px;
}

.novel-info {
    padding: 0.75rem;
}

.novel-card .novel-title {
    margin: 0.5rem 0;
    font-size: 0.9rem;
    line-height: 1.4;
}

.novel-card .novel-title a {
    color: #333;
    text-decoration: none;
}

.novel-card .novel-title a:hover {
    color: #007bff;
}

.novel-meta {
    font-size: 13px;
    color: #8f95a3;
}

.novel-meta .author {
    display: block;
    margin-bottom: 0.25rem;
}

.novel-meta .genres {
    margin-top: 0.5rem;
}

.novel-meta .badge {
    font-size: 0.75rem;
    font-weight: 500;
    margin-right: 0.25rem;
}

/* Latest Chapter Releases */
.chapter-releases {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    padding: 1.5rem;
    margin-bottom: 2rem;
}

.chapter-releases .section-title {
    font-size: 1.25rem;
    color: #2c3e50;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #e9ecef;
}

.chapter-release-item {
    display: flex;
    gap: 0.75rem;
    padding: 0.75rem;
    border-bottom: 1px solid #edf2f7;
}

.chapter-release-item:last-child {
    border-bottom: none;
}

.chapter-release-cover {
    width: 45px;
    height: 65px;
    flex-shrink: 0;
}

.chapter-release-cover img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 4px;
}

.chapter-release-info {
    flex: 1;
    min-width: 0;
    padding-top: 2px;
}

.chapter-release-title {
    font-size: 0.95rem;
    font-weight: 600;
    color: #2c3e50;
    text-decoration: none;
    margin-bottom: 0.25rem;
    display: block;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 800px;
}

.chapter-release-title:hover {
    color: #3498db;
}

.chapter-release-cover {
    width: 50px;
    height: 70px;
    flex-shrink: 0;
}

.chapter-release-cover img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 4px;
}

.chapter-release-info {
    flex: 1;
    min-width: 0;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
}

.chapter-release-title {
    font-size: 1rem;
    font-weight: 600;
    margin: 0 0 0.5rem;
    color: #2c3e50;
    text-decoration: none;
}

.chapter-release-title:hover {
    color: #3498db;
    text-decoration: none;
}

/* Navigation Tabs */
.nav-tabs {
    border: none;
    margin-bottom: 1.5rem;
    gap: 0.5rem;
}

.nav-tabs .nav-link {
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 6px;
    color: #6c757d;
    background: #e9ecef;
    transition: all 0.2s ease;
}

.nav-tabs .nav-link:hover {
    color: #2c3e50;
    background: #dee2e6;
}

.nav-tabs .nav-link.active {
    color: #fff;
    background: #3498db;
}

/* Popular Novels Section */
.popular-novels {
    background: #fff;
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.popular-novels .section-title {
    font-size: 1.25rem;
    color: #2c3e50;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #e9ecef;
}

.popular-item {
    display: flex;
    gap: 1rem;
    padding: 1rem;
    border-radius: 6px;
    background: #fff;
    border: 1px solid #e9ecef;
    margin-bottom: 1rem;
    transition: transform 0.2s ease;
}

.popular-item:hover {
    transform: translateX(4px);
}

.popular-item-cover {
    width: 80px;
    height: 120px;
    border-radius: 4px;
    overflow: hidden;
}

.popular-item-cover img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.popular-item-chapter {
    margin-top: 0.25rem;
    width: 120%;
}

.popular-item-chapter .d-flex {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.popular-item-chapter a {
    color: #2c3e50;
    text-decoration: none;
    font-size: 0.9rem;
    margin-right: 1rem;
    white-space: nowrap;
}

.popular-item-time {
    font-size: 0.8rem;
    color: #7f8c8d;
    margin: 2px 0;
}

.chapter-time {
    font-size: 0.85rem;
    color: #718096;
    white-space: nowrap;
    flex-shrink: 0;
    min-width: 85px;
    text-align: right;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .novel-grid {
        grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
    }

    .site-content {
        padding: 1rem 0;
    }

    .chapter-releases,
    .popular-novels {
        padding: 1rem;
    }
}

/* Reading Container */
.reading-container {
    max-width: 100%;
    margin: 0 auto;
    padding: 0 15px;
}

@media (min-width: 768px) {
    .reading-container {
        max-width: 900px;
        padding: 0;
    }
}

/* Chapter Content */
.chapter-content {
    font-size: 1.125rem;
    line-height: 1.8;
    padding: 1rem;
}

@media (max-width: 768px) {
    /* Typography */
    body {
        font-size: 14px;
    }

    h1, .h1 {
        font-size: 1.5rem;
    }

    h2, .h2 {
        font-size: 1.25rem;
    }

    /* Navigation */
    .navbar-brand {
        font-size: 1.25rem;
    }

    .navbar-nav {
        padding: 1rem 0;
    }

    /* Novel Cards */
    .novel-card {
        margin-bottom: 1rem;
    }

    .novel-card .card-body {
        padding: 0.75rem;
    }

    /* Chapter Navigation */
    .chapter-nav {
        padding: 0.75rem !important;
    }

    .chapter-nav .btn {
        padding: 0.375rem 0.75rem;
        font-size: 0.875rem;
    }

    /* Reading Experience */
    .chapter-content {
        font-size: 1rem;
        line-height: 1.6;
        padding: 0.75rem;
    }

    .chapter-title {
        font-size: 1.25rem;
    }

    /* Comments Section */
    .comments-section {
        padding: 0.75rem !important;
    }

    /* Social Share */
    .social-share {
        flex-wrap: wrap;
        justify-content: center;
        gap: 0.5rem;
    }

    /* Novel Details */
    .novel-meta {
        display: grid;
        grid-template-columns: 1fr;
        gap: 0.5rem;
    }

    .novel-cover {
        max-width: 200px;
        margin: 0 auto 1rem;
    }

    /* Search and Filters */
    .search-box {
        width: 100%;
        margin-bottom: 1rem;
    }

    .filter-section {
        flex-direction: column;
        gap: 0.5rem;
    }

    .filter-section select {
        width: 100%;
    }
}

/* Touch Improvements */
@media (hover: none) {
    .btn, 
    .nav-link,
    .chapter-link {
        padding: 0.75rem 1rem;
    }

    .novel-card {
        cursor: pointer;
    }

    select, 
    input[type="text"],
    input[type="search"] {
        font-size: 16px !important; /* Prevents zoom on iOS */
    }
}

/* Sidebar */
.sidebar .card {
    border: none;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-bottom: 1.5rem;
}

.sidebar .card-header {
    background-color: #fff;
    border-bottom: 1px solid #e9ecef;
    padding: 1rem;
}

.sidebar .card-header h5 {
    font-size: 1.1rem;
    font-weight: 600;
    margin: 0;
}

.sidebar .card-body {
    padding: 1rem;
}

/* Popular Novels */
.popular-novel-item {
    align-items: center;
}

.popular-novel-item .novel-thumb {
    width: 50px;
    height: 70px;
    border-radius: 4px;
    overflow: hidden;
}

.popular-novel-item .novel-info h6 {
    font-size: 0.9rem;
    margin-bottom: 0.25rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Latest Chapters */
.chapter-item {
    padding: 0.5rem 0;
    border-bottom: 1px solid #e9ecef;
}

.chapter-item:last-child {
    border-bottom: none;
}

.chapter-item .text-muted {
    font-size: 0.75rem;
}

.chapter-link {
    font-size: 0.9rem;
    color: #333;
}

.chapter-link:hover {
    color: #007bff;
}

/* Latest Releases */
.latest-releases {
    flex: 1;
    max-width: 1200px;
    margin-right: 0.9rem;
}

.chapter-release-item {
    display: flex;
    gap: 1rem;
    padding: 1rem;
    background: #fff;
    border: 1px solid #edf2f7;
    border-radius: 8px;
    margin-bottom: 1rem;
}

.chapter-release-item:last-child {
    margin-bottom: 0;
}

.popular-novels {
    width: 300px;
    flex-shrink: 0;
}

@media (max-width: 1200px) {
    .latest-releases {
        max-width: 100%;
        margin-right: 0;
        margin-bottom: 2rem;
    }
    
    .popular-novels {
        width: 100%;
    }
}

/* Chapters Table */
.chapters-list-header {
    padding: 1rem;
    background: #fff;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    margin-bottom: 1rem;
}

.chapters-list-header h2 {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
}

.bulk-actions {
    padding: 0.5rem 1rem;
    background: #fff;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.chapters-table-wrapper {
    background: #fff;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    overflow-x: auto;
}

.chapters-table {
    width: 100%;
    border-collapse: collapse;
}

.chapters-table th,
.chapters-table td {
    padding: 0.75rem;
    border-bottom: 1px solid #e9ecef;
    vertical-align: middle;
}

.chapters-table th {
    text-align: left;
    font-weight: 600;
    color: #495057;
    background: #f8f9fa;
    white-space: nowrap;
}

.chapters-table .check-column {
    width: 40px;
    text-align: center;
}

.chapters-table .novel-title-column {
    min-width: 200px;
    max-width: 300px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.chapters-table .chapter-title-column {
    min-width: 200px;
}

.chapters-table .chapter-title-column a {
    color: #333;
    text-decoration: none;
}

.chapters-table .chapter-title-column a:hover {
    color: #007bff;
}

.chapters-table .volume-column,
.chapters-table .chapter-number-column {
    width: 80px;
    text-align: center;
}

.chapters-table .date-column {
    width: 120px;
    color: #6c757d;
    white-space: nowrap;
}

.chapters-table tr:hover {
    background-color: #f8f9fa;
}

.chapters-table .no-chapters {
    text-align: center;
    color: #6c757d;
    padding: 2rem;
}

.chapters-table .pagination-row {
    text-align: center;
    background: #f8f9fa;
}

.chapters-table .pagination {
    display: inline-flex;
    padding: 1rem 0;
    margin: 0;
}

/* Search Box */
.search-box {
    position: relative;
}

.search-box input {
    padding-right: 30px;
    width: 200px;
}

/* Pagination */
.pagination {
    margin-top: 2rem;
    justify-content: center;
}

.pagination .page-numbers {
    display: inline-block;
    padding: 0.5rem 1rem;
    margin: 0 0.25rem;
    border-radius: 4px;
    background: #fff;
    color: #333;
    text-decoration: none;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.pagination .current {
    background: #007bff;
    color: #fff;
}

/* Novel Detail Page */
.novel-header {
    background: #fff;
    padding: 2rem;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.novel-cover {
    position: relative;
    width: 100%;
    max-width: 250px;
    margin: 0 auto;
    float: left;
    margin-right: 20px;
    margin-bottom: 20px;
}

.novel-cover img {
    width: 100%;
    height: auto;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    display: block;
}

.novel-score {
    font-size: 1.1rem;
    font-weight: 600;
    color: #333;
}

.novel-score .score {
    margin-bottom: 0.5rem;
}

.rating {
    text-align: center;
}

.stars {
    display: inline-flex;
    gap: 4px;
}

.stars i {
    color: #ffc107;
    font-size: 1.25rem;
    cursor: pointer;
    transition: transform 0.2s;
}

.stars i:hover {
    transform: scale(1.2);
}

.rating-count {
    font-size: 0.85rem;
    color: #6c757d;
}

.novel-actions .btn {
    font-weight: 500;
}

.novel-actions .badge {
    font-size: 0.8rem;
    font-weight: 500;
    padding: 0.4em 0.8em;
    text-decoration: none;
    background: #f8f9fa;
    color: #333;
    border: 1px solid #dee2e6;
}

.novel-actions .badge:hover {
    background: #e9ecef;
}

.novel-title {
    font-size: 1.75rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: #333;
    line-height: 1.3;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    white-space: normal;
}

.novel-meta {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 1rem;
    font-size: 13px;
    color: #8f95a3;
}

.meta-item {
    color: #666;
}

.meta-item strong {
    color: #333;
    display: inline-block;
    margin-right: 0.5rem;
}

.novel-excerpt {
    font-style: italic;
    color: #666;
    border-left: 3px solid #007bff;
    padding-left: 1rem;
}

.novel-synopsis {
    position: relative;
    color: #4a4a4a;
    line-height: 1.6;
    margin-bottom: 2rem;
    font-size: 0.95rem;
}

.novel-synopsis .read-more {
    color: #007bff;
    text-decoration: none;
    cursor: pointer;
    padding: 0;
    border: none;
    background: none;
}

.novel-synopsis .read-more:hover {
    text-decoration: underline;
}

.novel-synopsis .full-synopsis {
    margin-top: 1rem;
}

.read-info {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 4px;
    font-size: 0.9rem;
    color: #666;
}

/* Chapters Section */
.chapters-section {
    background: #fff;
    padding: 2rem;
    border-radius: 4px;
    margin-top: 2rem;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.chapters-section h2 {
    font-size: 1.5rem;
    margin-bottom: 1.5rem;
    color: #333;
}

.volume-section {
    margin-bottom: 2rem;
}

.volume-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #495057;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #dee2e6;
}

.chapter-item {
    padding: 0.75rem 0;
    border-bottom: 1px solid #eee;
    font-size: 13px;
    line-height: 1.4;
}

.chapter-item:last-child {
    border-bottom: none;
}

.chapter-link {
    color: #333;
    text-decoration: none;
    font-size: 0.95rem;
}

.chapter-link:hover {
    color: #007bff;
}

.chapter-date {
    font-size: 0.85rem;
    color: #718096;
    white-space: nowrap;
}

.chapter-time {
    font-size: 0.85rem;
    color: #718096;
    white-space: nowrap;
}

.chapter-item .chapter-title {
    font-weight: 400;
}

/* Responsive */
@media (max-width: 768px) {
    .novel-header {
        padding: 1rem;
    }

    .novel-cover {
        max-width: 200px;
        margin: 0 auto 1.5rem;
        padding-bottom: 266px; /* Fixed height for mobile */
    }

    .novel-title {
        text-align: center;
        font-size: 1.5rem;
        margin-top: 1rem;
    }

    .novel-meta {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .chapter-item {
        flex-direction: column;
        gap: 0.5rem;
        text-align: center;
    }

    .chapter-date {
        margin-left: 0;
    }
}

/* Novel Sidebar */
.novel-sidebar {
    position: sticky;
    top: 20px;
}

/* AdSense-friendly Ad Placements */
.ad-widget {
    margin: 1.5rem 0;
    text-align: center;
    clear: both;
}

.ad-widget:empty {
    display: none;
}

/* Header Ad Placement */
.header-ads {
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    padding: 1rem 0;
    margin-bottom: 1rem;
}

.header-ads .ad-widget {
    margin: 0;
}

/* Content Ad Placements */
.content-top-ads,
.content-middle-ads,
.content-bottom-ads {
    background: #fff;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 1rem;
    margin: 2rem 0;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.content-top-ads {
    margin-top: 0;
}

.content-bottom-ads {
    margin-bottom: 0;
}

/* Sidebar Ad Placements */
.sidebar-top-ads,
.sidebar-middle-ads,
.sidebar-bottom-ads {
    background: #fff;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 1rem;
    margin: 1rem 0;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

/* Footer Ad Placement */
.footer-ads {
    background: #f8f9fa;
    border-top: 1px solid #e9ecef;
    padding: 2rem 0;
    margin-top: 2rem;
    text-align: center;
}

/* Chapter-specific Ad Placements */
.chapter-top-ads,
.chapter-bottom-ads {
    background: #fff;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 1.5rem;
    margin: 2rem 0;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* Novel Page Ad Placements */
.novel-page-ads {
    background: #fff;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 1rem;
    margin: 1.5rem 0;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

/* Responsive Ad Styles */
@media (max-width: 768px) {
    .ad-widget {
        margin: 1rem 0;
    }

    .content-top-ads,
    .content-middle-ads,
    .content-bottom-ads,
    .chapter-top-ads,
    .chapter-bottom-ads {
        padding: 1rem;
        margin: 1.5rem 0;
    }

    .header-ads {
        padding: 0.75rem 0;
    }

    .footer-ads {
        padding: 1.5rem 0;
    }
}

/* Ad Label Styling */
.ad-widget .widget-title {
    font-size: 0.75rem;
    color: #6c757d;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 0.5rem;
    text-align: center;
}

/* Ensure ads don't interfere with reading experience */
.reading-container .ad-widget {
    max-width: 100%;
    overflow: hidden;
}

/* AdSense responsive container */
.adsense-container {
    width: 100%;
    text-align: center;
    margin: 1rem 0;
}

.adsense-container ins {
    display: block;
    margin: 0 auto;
}

.novel-sidebar .card {
    margin-bottom: 1.5rem;
    border-radius: 8px;
    overflow: hidden;
}

.novel-sidebar .card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    padding: 1rem;
}

.novel-sidebar .card-header h5 {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 600;
    color: #495057;
}

.novel-sidebar .card-body {
    padding: 1rem;
}

.novel-genres {
    margin-top: 1rem;
}

.novel-genres .badge {
    font-size: 0.85rem;
    font-weight: 500;
    padding: 0.4em 0.8em;
    margin: 0.2rem;
    background-color: #6c757d;
    color: #fff;
    text-decoration: none;
    transition: background-color 0.2s;
}

.novel-genres .badge:hover {
    background-color: #5a6268;
}

.novel-rating {
    text-align: center;
}

.novel-rating .stars {
    display: inline-block;
    cursor: pointer;
}

.novel-rating .stars i {
    color: #ffc107;
    font-size: 1.2rem;
    margin: 0 2px;
    transition: all 0.2s ease;
}

.novel-rating .stars i:hover {
    transform: scale(1.2);
}

.novel-rating .rating-count {
    color: #666;
    font-size: 0.9rem;
}

.info-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 0.5rem;
    font-size: 0.9rem;
}

.info-item {
    display: flex;
    justify-content: space-between;
    padding: 0.25rem 0;
    border-bottom: 1px solid rgba(0,0,0,0.1);
}

.info-label {
    color: #666;
    font-weight: 500;
}

.info-value {
    text-align: right;
}

.chapter-content {
    max-width: 1000px;
    margin: 0 auto;
    line-height: 1.8;
    background-color: #fff;
}

.chapter-title {
    font-size: 1.5rem;
    margin-bottom: 2rem;
    color: #333;
}

.chapter-text {
    margin-bottom: 2rem;
}

.chapter-text p {
    margin-bottom: 1.5rem;
}

.breadcrumb {
    padding: 0;
    margin-bottom: 1rem;
    background-color: transparent;
}

.breadcrumb-item a {
    color: #1a73e8;
    text-decoration: none;
}

.breadcrumb-item.active {
    color: #666;
}

.chapter-title {
    color: #1a73e8;
    font-size: 24px;
    margin-bottom: 0.5rem;
}

.chapter-subtitle {
    color: #333;
    font-size: 20px;
    margin-bottom: 1rem;
}

.chapter-meta {
    color: #666;
    font-size: 14px;
    margin-bottom: 1.5rem;
}

.btn {
    font-size: 14px;
    padding: 6px 12px;
    border-radius: 4px;
}

.btn i {
    margin-right: 6px;
}

.btn-primary {
    background-color: #1a73e8;
    border-color: #1a73e8;
}

.btn-light {
    background-color: #f8f9fa;
    border-color: #ddd;
    color: #333;
}

.btn-facebook {
    background-color: #1877f2;
    border-color: #1877f2;
    color: white;
}

.btn-twitter {
    background-color: #1da1f2;
    border-color: #1da1f2;
    color: white;
}

.btn-whatsapp {
    background-color: #25d366;
    border-color: #25d366;
    color: white;
}

.btn-pinterest {
    background-color: #e60023;
    border-color: #e60023;
    color: white;
}

.chapter-content {
    font-family: 'Fira Sans', sans-serif;
    font-size: 16px;
    line-height: 160%;
    color: #333;
    background-color: #fff;
    padding: 2rem;
    margin-top: 1.5rem;
}

@media (max-width: 768px) {
    .btn {
        padding: 4px 8px;
        font-size: 13px;
    }
    
    .chapter-title {
        font-size: 20px;
    }
    
    .chapter-subtitle {
        font-size: 16px;
    }
    
    .chapter-actions {
        flex-wrap: wrap;
        gap: 0.5rem;
    }
    
    .chapter-social {
        flex-wrap: wrap;
    }
}

.card-title {
    font-family: 'Fira Sans', sans-serif;
    font-size: 14px;
    font-weight: 500;
    line-height: 1.3;
    margin-bottom: 5px;
}

small, .small {
    font-size: 12px;
    color: #8f95a3;
}

.synopsis {
    font-size: 13px;
    line-height: 1.5;
    color: #444;
}

.navbar-nav {
    font-family: 'Fira Sans', sans-serif;
    font-size: 13px;
    font-weight: 500;
}

.btn {
    font-family: 'Fira Sans', sans-serif;
    font-size: 13px;
    font-weight: 500;
    padding: 4px 8px;
}

.btn-sm {
    font-size: 12px;
    padding: 2px 6px;
}

.reading-progress {
    font-size: 12px;
    color: #8f95a3;
}

/* Novel List Items */
.novel-item h3 {
    font-size: 0.95rem;
    font-weight: 500;
    margin-bottom: 0.5rem;
}

/* Chapter Item Styling */
.latest-releases .popular-item-chapter {
    margin-top: 0.25rem;
    width: 110%;
}

.popular-section .popular-item-chapter {
    margin-top: 0.25rem;
    width: 100%;
}

.popular-item-chapter .d-flex {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width:100%
}

.popular-item-chapter a {
    color: #2c3e50;
    text-decoration: none;
    font-size: 0.9rem;
    margin-right: 1rem;
    white-space: nowrap;
}

.popular-item-chapter a:hover {
    color: #3498db;
}

.popular-item-time {
    font-size: 0.8rem;
    color: #7f8c8d;
    margin: 2px 0;
}

/* Rating Stars */
.rating-stars {
    display: inline-flex;
    gap: 2px;
}

.rating-stars i {
    font-size: 0.9rem;
}

.rating-value {
    font-size: 0.9rem;
    font-weight: 500;
    color: #2c3e50;
}

.chapter-release-rating {
    margin-top: 4px;
}

.chapter-breadcrumb .breadcrumb {
    margin-bottom: 0;
    font-size: 14px;
}

.chapter-header {
    margin: 2rem 0;
}

.chapter-title {
    font-size: 2rem;
    margin-bottom: 0.5rem;
    color: #2196F3;
}

.chapter-subtitle {
    font-size: 1.25rem;
    color: #666;
    margin-bottom: 1rem;
}

.chapter-meta {
    font-size: 14px;
    color: #666;
}

.chapter-actions {
    margin: 1.5rem 0;
}

.btn {
    border-radius: 4px;
    padding: 0.5rem 1rem;
    font-size: 14px;
}

.btn-facebook {
    background-color: #1877f2;
    color: white;
}

.btn-twitter {
    background-color: #1da1f2;
    color: white;
}

.btn-whatsapp {
    background-color: #25d366;
    color: white;
}

.btn-pinterest {
    background-color: #e60023;
    color: white;
}

.chapter-social .btn {
    padding: 0.5rem 1rem;
}

.chapter-social .btn i {
    margin-right: 0.5rem;
}

.chapter-content {
    font-family: 'Fira Sans', sans-serif;
    font-size: 16px;
    line-height: 160%;
    color: #333;
    max-width: 1000px;
    margin: 0 auto;
    padding: 2rem;
}

@media (max-width: 768px) {
    .chapter-title {
        font-size: 1.5rem;
    }
    
    .chapter-subtitle {
        font-size: 1rem;
    }
    
    .chapter-actions {
        flex-wrap: wrap;
        gap: 0.5rem;
    }
    
    .chapter-social {
        flex-wrap: wrap;
    }
}

/* Modal Options */
.modal-content {
    border-radius: 8px;
}

.modal-header {
    border-bottom: 1px solid #dee2e6;
    padding: 1rem;
}

.modal-title {
    font-size: 1.25rem;
    font-weight: 500;
}

.modal-body {
    padding: 1rem;
}

.form-label {
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.form-select {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    padding: 0.5rem;
    border-radius: 4px;
    margin-bottom: 0.5rem;
}

.text-muted {
    font-size: 0.875rem;
}

#resetOptions {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    color: #333;
    padding: 0.5rem 1rem;
    border-radius: 4px;
}

#resetOptions:hover {
    background-color: #e9ecef;
}

/* Enhanced Layout Styles for Redesigned Theme */

/* Enhanced Footer Styles */
.site-footer {
    background: var(--background-primary);
    border-top: 1px solid var(--border-color);
    padding: var(--spacing-2xl) 0 var(--spacing-lg);
    margin-top: var(--spacing-2xl);
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--spacing-2xl);
    margin-bottom: var(--spacing-xl);
}

.footer-section {
    position: relative;
}

.footer-title {
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
    font-size: var(--font-size-xl);
    font-weight: 700;
}

.footer-section-title {
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
    font-size: var(--font-size-lg);
    font-weight: 600;
}

.footer-description {
    color: var(--text-secondary);
    line-height: var(--line-height-relaxed);
    margin-bottom: var(--spacing-md);
}

.footer-navigation {
    margin-top: var(--spacing-sm);
}

.footer-menu {
    list-style: none;
    padding: 0;
    margin: 0;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.footer-menu li {
    position: relative;
}

.footer-menu a {
    color: var(--text-secondary);
    text-decoration: none;
    transition: color var(--transition-fast);
    padding: var(--spacing-xs) 0;
    display: inline-block;
    position: relative;
}

.footer-menu a:hover,
.footer-menu a:focus {
    color: var(--primary-color);
}

.footer-menu a::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--primary-color);
    transition: width var(--transition-normal);
}

.footer-menu a:hover::after {
    width: 100%;
}

.social-links {
    display: flex;
    gap: var(--spacing-md);
    margin-top: var(--spacing-sm);
}

.social-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 44px;
    height: 44px;
    background: var(--background-secondary);
    color: var(--text-secondary);
    border-radius: 50%;
    text-decoration: none;
    transition: all var(--transition-normal);
    font-size: var(--font-size-lg);
}

.social-link:hover,
.social-link:focus {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.footer-bottom {
    border-top: 1px solid var(--border-light);
    padding-top: var(--spacing-lg);
}

.footer-bottom-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: var(--spacing-md);
}

.copyright {
    color: var(--text-secondary);
    margin: 0;
    font-size: var(--font-size-sm);
}

.copyright a {
    color: var(--text-primary);
    text-decoration: none;
    font-weight: 500;
}

.copyright a:hover {
    color: var(--primary-color);
}

.admin-info {
    font-size: var(--font-size-xs);
    color: var(--text-muted);
}

.status-enabled {
    color: var(--secondary-color);
    font-weight: 600;
}

.status-disabled {
    color: var(--accent-color);
    font-weight: 600;
}

/* Novel Detail Page Styles */
.novel-detail-container {
    padding: var(--spacing-lg) 0;
}

.novel-header {
    margin-bottom: var(--spacing-2xl);
}

.novel-cover-section {
    position: sticky;
    top: calc(var(--spacing-xl) + 60px);
}

.novel-cover-container {
    position: relative;
    border-radius: var(--radius-xl);
    overflow: hidden;
    box-shadow: var(--shadow-heavy);
    margin-bottom: var(--spacing-lg);
    aspect-ratio: 3/4;
}

.novel-cover-main {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform var(--transition-slow);
}

.novel-cover-container:hover .novel-cover-main {
    transform: scale(1.05);
}

.novel-cover-placeholder {
    width: 100%;
    height: 100%;
    background: var(--background-tertiary);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: var(--text-muted);
    font-size: var(--font-size-lg);
    gap: var(--spacing-sm);
}

.novel-cover-placeholder i {
    font-size: var(--font-size-4xl);
}

.novel-status-overlay {
    position: absolute;
    top: var(--spacing-md);
    right: var(--spacing-md);
}

.status-badge {
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-lg);
    font-size: var(--font-size-xs);
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    color: white;
    backdrop-filter: blur(10px);
    box-shadow: var(--shadow-light);
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.status-badge--ongoing {
    background: rgba(52, 168, 83, 0.9);
}

.status-badge--completed {
    background: rgba(26, 115, 232, 0.9);
}

.status-badge--hiatus {
    background: rgba(255, 193, 7, 0.9);
}

.status-badge--dropped {
    background: rgba(234, 67, 53, 0.9);
}

.novel-actions {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.btn-action {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md);
    font-weight: 600;
    border-radius: var(--radius-lg);
    transition: all var(--transition-normal);
    text-decoration: none;
    border: 2px solid transparent;
}

.btn-action:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.btn-action.btn-primary {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.btn-action.btn-primary:hover {
    background: var(--accent-color);
    border-color: var(--accent-color);
    color: white;
}

.btn-action.btn-success {
    background: var(--secondary-color);
    color: white;
    border-color: var(--secondary-color);
}

.btn-action.btn-success:hover {
    background: #2d7a3d;
    border-color: #2d7a3d;
    color: white;
}

.btn-action.btn-outline-primary {
    background: transparent;
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-action.btn-outline-primary:hover {
    background: var(--primary-color);
    color: white;
}

/* Chapter Reading Styles */
.chapter-container {
    max-width: 800px;
    margin: 0 auto;
    padding: var(--spacing-lg) var(--spacing-md);
}

.chapter-content {
    font-family: var(--font-family-reading);
    font-size: var(--font-size-lg);
    line-height: var(--line-height-relaxed);
    color: var(--text-primary);
    background: var(--background-primary);
    padding: var(--spacing-2xl);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-light);
    margin: var(--spacing-xl) 0;
}

.chapter-content p {
    margin-bottom: var(--spacing-lg);
    text-align: justify;
}

.chapter-content p:last-child {
    margin-bottom: 0;
}

.chapter-navigation {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-lg);
    background: var(--background-primary);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-light);
    margin: var(--spacing-xl) 0;
    gap: var(--spacing-md);
}

.chapter-nav-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-lg);
    background: var(--primary-color);
    color: white;
    text-decoration: none;
    border-radius: var(--radius-lg);
    font-weight: 600;
    transition: all var(--transition-normal);
    border: none;
    cursor: pointer;
}

.chapter-nav-btn:hover {
    background: var(--accent-color);
    color: white;
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.chapter-nav-btn:disabled {
    background: var(--background-tertiary);
    color: var(--text-muted);
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.chapter-nav-center {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
}

/* Enhanced Mobile-First Responsive Design */
@media (max-width: 1200px) {
    .container {
        max-width: 100%;
        padding: 0 var(--spacing-lg);
    }
}

@media (max-width: 992px) {
    .hero-title {
        font-size: var(--font-size-3xl);
    }

    .section-header {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-sm);
    }

    .novels-grid {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: var(--spacing-md);
    }

    .genres-grid {
        grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    }
}

@media (max-width: 768px) {
    :root {
        --spacing-xs: 0.25rem;
        --spacing-sm: 0.5rem;
        --spacing-md: 0.75rem;
        --spacing-lg: 1rem;
        --spacing-xl: 1.5rem;
        --spacing-2xl: 2rem;
    }

    .container {
        padding: 0 var(--spacing-md);
    }

    .hero-section {
        padding: var(--spacing-xl) 0;
    }

    .hero-title {
        font-size: var(--font-size-2xl);
    }

    .hero-description {
        font-size: var(--font-size-base);
    }

    .hero-actions {
        flex-direction: column;
        align-items: center;
    }

    .hero-actions .btn {
        width: 100%;
        max-width: 280px;
    }

    .novels-grid {
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
        gap: var(--spacing-sm);
    }

    .genres-grid {
        grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
        gap: var(--spacing-sm);
    }

    .genre-link {
        padding: var(--spacing-md);
    }

    .genre-icon {
        font-size: var(--font-size-xl);
    }

    .popular-link {
        padding: var(--spacing-xs);
    }

    .popular-rank {
        width: 28px;
        height: 28px;
        font-size: var(--font-size-xs);
    }

    .popular-cover {
        width: 40px;
        height: 56px;
    }

    .footer-bottom-content {
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-sm);
    }

    .novel-cover-section {
        position: static;
        margin-bottom: var(--spacing-lg);
    }

    .social-links {
        justify-content: center;
    }

    .chapter-container {
        padding: var(--spacing-md);
    }

    .chapter-content {
        padding: var(--spacing-lg);
        font-size: var(--font-size-base);
    }

    .chapter-navigation {
        flex-direction: column;
        gap: var(--spacing-sm);
    }

    .chapter-nav-btn {
        width: 100%;
        justify-content: center;
    }

    .breadcrumb-navigation {
        margin: var(--spacing-sm) 0;
    }

    .breadcrumb {
        font-size: var(--font-size-xs);
        flex-wrap: wrap;
    }
}

@media (max-width: 576px) {
    .novels-grid {
        grid-template-columns: 1fr;
    }

    .genres-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .hero-section {
        padding: var(--spacing-lg) 0;
    }

    .section-spacing {
        margin-bottom: var(--spacing-xl);
    }

    .navbar-nav {
        text-align: center;
    }

    .navbar-nav .nav-link {
        padding: var(--spacing-sm);
    }

    .navbar form {
        margin-top: var(--spacing-sm);
        width: 100%;
    }

    .navbar .input-group {
        width: 100%;
    }

    .popular-list {
        padding: var(--spacing-xs);
    }

    .popular-item {
        margin-bottom: var(--spacing-xs);
    }

    .novel-actions {
        gap: var(--spacing-xs);
    }

    .btn-action {
        padding: var(--spacing-sm);
        font-size: var(--font-size-sm);
    }
}

/* Print Styles */
@media print {
    .site-header,
    .breadcrumb-navigation,
    .chapter-navigation,
    .footer-ads,
    .content-top-ads,
    .content-middle-ads,
    .content-bottom-ads,
    .header-ads {
        display: none !important;
    }

    .chapter-content {
        box-shadow: none;
        border: 1px solid #ccc;
        font-size: 12pt;
        line-height: 1.4;
    }

    body {
        font-size: 12pt;
        color: #000;
        background: #fff;
    }

    a {
        color: #000;
        text-decoration: underline;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    :root {
        --text-primary: #000000;
        --text-secondary: #333333;
        --background-primary: #ffffff;
        --background-secondary: #f0f0f0;
        --border-color: #000000;
        --primary-color: #0000ff;
        --accent-color: #ff0000;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }

    .novel-cover-main,
    .genre-icon,
    .btn-action,
    .popular-link,
    .chapter-nav-btn {
        transform: none !important;
    }
}

/* Enhanced Mobile Touch Interactions */
@media (hover: none) and (pointer: coarse) {
    /* Touch-friendly button sizes */
    .btn-action,
    .chapter-nav-btn,
    .share-btn {
        min-height: 44px;
        min-width: 44px;
        padding: var(--spacing-md) var(--spacing-lg);
    }

    /* Larger tap targets for mobile */
    .novel-card,
    .chapter-link,
    .genre-link,
    .popular-link {
        padding: var(--spacing-md);
    }

    /* Remove hover effects on touch devices */
    .novel-card:hover,
    .chapter-link:hover,
    .genre-link:hover,
    .popular-link:hover {
        transform: none;
    }

    /* Optimize scrolling performance */
    .chapter-content,
    .novels-grid,
    .chapter-list {
        -webkit-overflow-scrolling: touch;
        scroll-behavior: smooth;
    }
}

/* Chapter Reading Enhancements */
.chapter-header {
    background: var(--background-primary);
    padding: var(--spacing-lg);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-light);
    margin-bottom: var(--spacing-lg);
}

.chapter-title-section {
    text-align: center;
}

.novel-breadcrumb {
    margin-bottom: var(--spacing-sm);
}

.novel-link {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
    font-size: var(--font-size-sm);
}

.novel-link:hover {
    text-decoration: underline;
}

.chapter-title {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    color: var(--text-primary);
    margin: 0 0 var(--spacing-sm) 0;
    line-height: var(--line-height-tight);
}

.chapter-subtitle {
    display: block;
    font-size: var(--font-size-lg);
    font-weight: 400;
    color: var(--text-secondary);
    margin-top: var(--spacing-xs);
}

.chapter-meta {
    display: flex;
    justify-content: center;
    gap: var(--spacing-md);
    font-size: var(--font-size-sm);
    color: var(--text-muted);
    margin-top: var(--spacing-sm);
}

.chapter-navigation {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-lg);
    background: var(--background-primary);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-light);
    margin: var(--spacing-xl) 0;
    gap: var(--spacing-md);
}

.nav-controls {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.chapter-nav-center {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.reading-settings {
    display: flex;
    gap: var(--spacing-sm);
}

.reading-settings-btn {
    width: 44px;
    height: 44px;
    border-radius: 50%;
    border: none;
    background: var(--background-secondary);
    color: var(--text-secondary);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all var(--transition-fast);
}

.reading-settings-btn:hover,
.reading-settings-btn.active {
    background: var(--primary-color);
    color: white;
}

.chapter-content {
    background: var(--background-primary);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-light);
    margin: var(--spacing-xl) 0;
    overflow: hidden;
}

.content-wrapper {
    padding: var(--spacing-2xl);
}

.chapter-text {
    font-family: var(--font-family-reading);
    font-size: var(--font-size-lg);
    line-height: var(--line-height-relaxed);
    color: var(--text-primary);
}

.chapter-text p {
    margin-bottom: var(--spacing-lg);
    text-align: justify;
}

.chapter-text p:last-child {
    margin-bottom: 0;
}

.no-content-message {
    text-align: center;
    padding: var(--spacing-2xl);
    color: var(--text-muted);
}

.no-content-message i {
    font-size: var(--font-size-2xl);
    margin-bottom: var(--spacing-md);
    color: var(--accent-color);
}

/* Social Share Section */
.social-share-section {
    background: var(--background-primary);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-light);
    padding: var(--spacing-lg);
    margin: var(--spacing-xl) 0;
    text-align: center;
}

.share-title {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 var(--spacing-md) 0;
}

.share-buttons {
    display: flex;
    justify-content: center;
    gap: var(--spacing-sm);
    flex-wrap: wrap;
}

.share-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-lg);
    text-decoration: none;
    font-weight: 500;
    transition: all var(--transition-normal);
    border: none;
    cursor: pointer;
    font-size: var(--font-size-sm);
}

.share-facebook {
    background: #1877f2;
    color: white;
}

.share-facebook:hover {
    background: #166fe5;
    color: white;
}

.share-twitter {
    background: #1da1f2;
    color: white;
}

.share-twitter:hover {
    background: #1a91da;
    color: white;
}

.share-whatsapp {
    background: #25d366;
    color: white;
}

.share-whatsapp:hover {
    background: #22c55e;
    color: white;
}

.share-copy {
    background: var(--background-secondary);
    color: var(--text-primary);
}

.share-copy:hover {
    background: var(--primary-color);
    color: white;
}

/* Novel Detail Page Enhancements */
.novel-header-info {
    margin-bottom: var(--spacing-xl);
}

.novel-title {
    font-size: var(--font-size-3xl);
    font-weight: 700;
    color: var(--text-primary);
    margin: 0 0 var(--spacing-md) 0;
    line-height: var(--line-height-tight);
}

.novel-meta-info {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-lg);
}

.novel-author,
.novel-year,
.novel-genres {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: var(--font-size-base);
    color: var(--text-secondary);
}

.genre-tag {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
}

.genre-tag:hover {
    text-decoration: underline;
}

.novel-description {
    margin-bottom: var(--spacing-xl);
}

.section-title {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 var(--spacing-md) 0;
    padding-bottom: var(--spacing-sm);
    border-bottom: 2px solid var(--border-light);
}

.description-content {
    font-size: var(--font-size-base);
    line-height: var(--line-height-relaxed);
    color: var(--text-primary);
}

.novel-statistics {
    margin-bottom: var(--spacing-xl);
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: var(--spacing-md);
}

.stat-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    background: var(--background-secondary);
    border-radius: var(--radius-lg);
}

.stat-icon {
    width: 48px;
    height: 48px;
    background: var(--primary-color);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-lg);
}

.stat-content {
    flex: 1;
}

.stat-value {
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--text-primary);
    line-height: 1;
}

.stat-label {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    margin-top: var(--spacing-xs);
}

/* Chapter List Enhancements */
.chapter-list-section {
    margin-bottom: var(--spacing-xl);
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
}

.chapter-controls {
    display: flex;
    gap: var(--spacing-sm);
}

.chapter-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.chapter-item {
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-light);
    transition: box-shadow var(--transition-normal);
}

.chapter-item:hover {
    box-shadow: var(--shadow-medium);
}

.chapter-link {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-md);
    background: var(--background-primary);
    color: var(--text-primary);
    text-decoration: none;
    transition: background-color var(--transition-fast);
}

.chapter-link:hover {
    background: var(--background-secondary);
    color: var(--text-primary);
}

.chapter-info {
    flex: 1;
    min-width: 0;
}

.chapter-number {
    font-weight: 600;
    color: var(--primary-color);
    font-size: var(--font-size-sm);
    margin-bottom: var(--spacing-xs);
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.premium-badge {
    color: #fbbf24;
    font-size: var(--font-size-xs);
}

.chapter-title {
    font-size: var(--font-size-base);
    font-weight: 500;
    color: var(--text-primary);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.chapter-meta {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    flex-shrink: 0;
}

.chapter-date {
    font-size: var(--font-size-xs);
    color: var(--text-muted);
}

.chapter-arrow {
    color: var(--text-muted);
    font-size: var(--font-size-sm);
    transition: transform var(--transition-fast);
}

.chapter-link:hover .chapter-arrow {
    transform: translateX(4px);
}

.no-chapters-message {
    text-align: center;
    padding: var(--spacing-2xl);
    color: var(--text-muted);
}

.no-chapters-message i {
    font-size: var(--font-size-4xl);
    margin-bottom: var(--spacing-md);
    color: var(--text-muted);
}

.no-chapters-message h3 {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-sm);
}

/* Enhanced UI Components */

/* Scroll to Top Button */
.scroll-to-top {
    position: fixed;
    bottom: var(--spacing-lg);
    right: var(--spacing-lg);
    width: 50px;
    height: 50px;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal);
    box-shadow: var(--shadow-medium);
}

.scroll-to-top.visible {
    opacity: 1;
    visibility: visible;
}

.scroll-to-top:hover {
    background: var(--accent-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-heavy);
}

/* Toast Notifications */
.toast {
    position: fixed;
    top: var(--spacing-lg);
    right: var(--spacing-lg);
    background: var(--background-primary);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-heavy);
    padding: var(--spacing-md);
    z-index: 1001;
    opacity: 0;
    transform: translateX(100%);
    transition: all var(--transition-normal);
    border-left: 4px solid var(--secondary-color);
}

.toast.toast-error {
    border-left-color: var(--accent-color);
}

.toast.show {
    opacity: 1;
    transform: translateX(0);
}

.toast-content {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    color: var(--text-primary);
    font-weight: 500;
}

.toast-content i {
    color: var(--secondary-color);
}

.toast.toast-error .toast-content i {
    color: var(--accent-color);
}

/* Reading Progress Bar */
.reading-progress-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background: var(--background-tertiary);
    z-index: 999;
}

.reading-progress {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    width: 0%;
    transition: width 0.3s ease;
}

/* Theme-specific styles */
[data-theme="dark"] {
    --text-primary: #e8eaed;
    --text-secondary: #9aa0a6;
    --text-muted: #80868b;
    --background-primary: #202124;
    --background-secondary: #303134;
    --background-tertiary: #3c4043;
    --border-color: #5f6368;
    --border-light: #3c4043;
}

/* Comprehensive Dark Mode Styles - also applied as default when no theme is set */
[data-theme="dark"] body,
body:not([data-theme]) {
    background: var(--background-primary) !important;
    color: var(--text-primary) !important;
}

[data-theme="dark"] .container,
[data-theme="dark"] .container-fluid,
body:not([data-theme]) .container,
body:not([data-theme]) .container-fluid {
    background: var(--background-primary) !important;
    color: var(--text-primary) !important;
}

[data-theme="dark"] .card,
[data-theme="dark"] .novel-card,
[data-theme="dark"] .chapter-releases,
[data-theme="dark"] .popular-novels,
[data-theme="dark"] .novel-header,
[data-theme="dark"] .chapters-section,
[data-theme="dark"] .chapter-content,
[data-theme="dark"] .chapter-nav,
[data-theme="dark"] .breadcrumb,
[data-theme="dark"] .reading-container,
body:not([data-theme]) .card,
body:not([data-theme]) .novel-card,
body:not([data-theme]) .chapter-releases,
body:not([data-theme]) .popular-novels,
body:not([data-theme]) .novel-header,
body:not([data-theme]) .chapters-section,
body:not([data-theme]) .chapter-content,
body:not([data-theme]) .chapter-nav,
body:not([data-theme]) .breadcrumb,
body:not([data-theme]) .reading-container {
    background: var(--card-background, #1a1a1a) !important;
    color: var(--text-primary) !important;
    border-color: var(--border-color) !important;
}

[data-theme="dark"] .card-body,
[data-theme="dark"] .card-header,
body:not([data-theme]) .card-body,
body:not([data-theme]) .card-header {
    background: var(--card-background, #1a1a1a) !important;
    color: var(--text-primary) !important;
    border-color: var(--border-color) !important;
}

[data-theme="dark"] .navbar,
[data-theme="dark"] .site-header,
body:not([data-theme]) .navbar,
body:not([data-theme]) .site-header {
    background: var(--navbar-background, #1a1a1a) !important;
    border-color: var(--border-color) !important;
}

[data-theme="dark"] .site-footer,
body:not([data-theme]) .site-footer {
    background: var(--footer-background, #1a1a1a) !important;
    border-color: var(--border-color) !important;
}

[data-theme="dark"] .modal-content,
[data-theme="dark"] .modal-header,
[data-theme="dark"] .modal-body,
[data-theme="dark"] .modal-footer {
    background: var(--modal-background, #1a1a1a) !important;
    color: var(--text-primary) !important;
    border-color: var(--border-color) !important;
}

[data-theme="dark"] .form-control,
[data-theme="dark"] .form-select,
[data-theme="dark"] input,
[data-theme="dark"] textarea,
[data-theme="dark"] select,
body:not([data-theme]) .form-control,
body:not([data-theme]) .form-select,
body:not([data-theme]) input,
body:not([data-theme]) textarea,
body:not([data-theme]) select {
    background: var(--input-background, #1a1a1a) !important;
    color: var(--text-primary) !important;
    border-color: var(--border-color) !important;
}

[data-theme="dark"] .form-control:focus,
[data-theme="dark"] .form-select:focus,
[data-theme="dark"] input:focus,
[data-theme="dark"] textarea:focus,
body:not([data-theme]) .form-control:focus,
body:not([data-theme]) .form-select:focus,
body:not([data-theme]) input:focus,
body:not([data-theme]) textarea:focus {
    background: var(--input-background, #1a1a1a) !important;
    color: var(--text-primary) !important;
    border-color: var(--primary-color) !important;
    box-shadow: 0 0 0 0.2rem rgba(26, 115, 232, 0.25) !important;
}

[data-theme="dark"] .dropdown-menu {
    background: var(--card-background, #1a1a1a) !important;
    border-color: var(--border-color) !important;
}

[data-theme="dark"] .dropdown-item {
    color: var(--text-primary) !important;
}

[data-theme="dark"] .dropdown-item:hover,
[data-theme="dark"] .dropdown-item:focus {
    background: var(--background-secondary) !important;
    color: var(--text-primary) !important;
}

[data-theme="dark"] .btn-light {
    background: var(--background-secondary) !important;
    border-color: var(--border-color) !important;
    color: var(--text-primary) !important;
}

[data-theme="dark"] .btn-secondary {
    background: var(--background-secondary) !important;
    border-color: var(--border-color) !important;
    color: var(--text-primary) !important;
}

/* Fix chapter navigation buttons in dark mode and default state */
[data-theme="dark"] .btn-primary,
[data-theme="dark"] .chapter-nav-btn.btn-primary,
body:not([data-theme]) .btn-primary,
body:not([data-theme]) .chapter-nav-btn.btn-primary {
    background: var(--primary-color) !important;
    border-color: var(--primary-color) !important;
    color: #ffffff !important;
}

[data-theme="dark"] .btn-primary:hover,
[data-theme="dark"] .chapter-nav-btn.btn-primary:hover,
body:not([data-theme]) .btn-primary:hover,
body:not([data-theme]) .chapter-nav-btn.btn-primary:hover {
    background: var(--accent-color) !important;
    border-color: var(--accent-color) !important;
    color: #ffffff !important;
}

[data-theme="dark"] .chapter-nav-btn {
    color: #ffffff !important;
}

[data-theme="dark"] .chapter-nav-btn.btn-outline {
    background: transparent !important;
    border-color: var(--primary-color) !important;
    color: var(--primary-color) !important;
}

[data-theme="dark"] .chapter-nav-btn.btn-outline:hover {
    background: var(--primary-color) !important;
    border-color: var(--primary-color) !important;
    color: #ffffff !important;
}

[data-theme="dark"] .chapter-nav-btn.btn-disabled,
[data-theme="dark"] .chapter-nav-btn:disabled {
    background: var(--background-tertiary) !important;
    border-color: var(--border-color) !important;
    color: var(--text-muted) !important;
}

/* Ensure text visibility in navigation buttons */
[data-theme="dark"] .chapter-nav-btn span,
[data-theme="dark"] .chapter-nav-btn i {
    color: inherit !important;
}

/* Additional fixes for button text visibility */
[data-theme="dark"] .btn-primary span,
[data-theme="dark"] .btn-primary i {
    color: #ffffff !important;
}

[data-theme="dark"] .chapter-navigation .btn-primary,
[data-theme="dark"] .chapter-nav .btn-primary {
    background: #1a73e8 !important;
    border-color: #1a73e8 !important;
    color: #ffffff !important;
}

[data-theme="dark"] .chapter-navigation .btn-primary:hover,
[data-theme="dark"] .chapter-nav .btn-primary:hover {
    background: #1557b0 !important;
    border-color: #1557b0 !important;
    color: #ffffff !important;
}

/* Force white text on all primary buttons in dark mode */
[data-theme="dark"] .btn-primary *,
[data-theme="dark"] .chapter-nav-btn.btn-primary * {
    color: #ffffff !important;
}

/* Fix outline buttons (Chapters button) for all themes */
.chapter-nav-btn.btn-outline {
    background: transparent !important;
    border: 2px solid var(--primary-color, #1a73e8) !important;
    color: var(--primary-color, #1a73e8) !important;
}

.chapter-nav-btn.btn-outline:hover {
    background: var(--primary-color, #1a73e8) !important;
    border-color: var(--primary-color, #1a73e8) !important;
    color: #ffffff !important;
}

.chapter-nav-btn.btn-outline span,
.chapter-nav-btn.btn-outline i {
    color: inherit !important;
}

/* Light theme specific fixes for outline buttons */
[data-theme="light"] .chapter-nav-btn.btn-outline {
    background: transparent !important;
    border: 2px solid #1a73e8 !important;
    color: #1a73e8 !important;
}

[data-theme="light"] .chapter-nav-btn.btn-outline:hover {
    background: #1a73e8 !important;
    border-color: #1a73e8 !important;
    color: #ffffff !important;
}

[data-theme="light"] .chapter-nav-btn.btn-outline span,
[data-theme="light"] .chapter-nav-btn.btn-outline i {
    color: inherit !important;
}

/* Default state (no theme) uses dark theme outline button styles */
body:not([data-theme]) .chapter-nav-btn.btn-outline {
    background: transparent !important;
    border: 2px solid var(--primary-color) !important;
    color: var(--primary-color) !important;
}

body:not([data-theme]) .chapter-nav-btn.btn-outline:hover {
    background: var(--primary-color) !important;
    border-color: var(--primary-color) !important;
    color: #ffffff !important;
}

body:not([data-theme]) .chapter-nav-btn.btn-outline span,
body:not([data-theme]) .chapter-nav-btn.btn-outline i {
    color: inherit !important;
}

[data-theme="dark"] .table {
    color: var(--text-primary) !important;
}

[data-theme="dark"] .table th,
[data-theme="dark"] .table td {
    border-color: var(--border-color) !important;
}

[data-theme="dark"] .table thead th {
    background: var(--background-secondary) !important;
    color: var(--text-primary) !important;
}

[data-theme="dark"] .table tbody tr:hover {
    background: var(--background-secondary) !important;
}

[data-theme="dark"] .chapters-table-wrapper,
[data-theme="dark"] .chapters-list-header,
[data-theme="dark"] .bulk-actions {
    background: var(--card-background, #1a1a1a) !important;
    color: var(--text-primary) !important;
}

[data-theme="dark"] .pagination .page-link {
    background: var(--card-background, #1a1a1a) !important;
    border-color: var(--border-color) !important;
    color: var(--text-primary) !important;
}

[data-theme="dark"] .pagination .page-link:hover {
    background: var(--background-secondary) !important;
    color: var(--text-primary) !important;
}

[data-theme="dark"] .pagination .page-item.active .page-link {
    background: var(--primary-color) !important;
    border-color: var(--primary-color) !important;
}

[data-theme="dark"] .alert {
    background: var(--background-secondary) !important;
    border-color: var(--border-color) !important;
    color: var(--text-primary) !important;
}

[data-theme="dark"] .badge {
    background: var(--background-secondary) !important;
    color: var(--text-primary) !important;
}

[data-theme="dark"] .list-group-item {
    background: var(--card-background, #1a1a1a) !important;
    border-color: var(--border-color) !important;
    color: var(--text-primary) !important;
}

[data-theme="dark"] .list-group-item:hover {
    background: var(--background-secondary) !important;
}

[data-theme="dark"] pre,
[data-theme="dark"] code,
[data-theme="dark"] blockquote {
    background: var(--background-secondary) !important;
    border-color: var(--border-color) !important;
    color: var(--text-primary) !important;
}

[data-theme="dark"] .chapter-text,
[data-theme="dark"] .content-wrapper,
[data-theme="dark"] .novel-synopsis,
[data-theme="dark"] .novel-excerpt {
    color: var(--text-primary) !important;
}

[data-theme="dark"] .chapter-title,
[data-theme="dark"] .novel-title,
[data-theme="dark"] .section-title {
    color: var(--text-primary) !important;
}

[data-theme="dark"] .chapter-subtitle,
[data-theme="dark"] .chapter-meta,
[data-theme="dark"] .novel-meta {
    color: var(--text-secondary) !important;
}

[data-theme="dark"] a {
    color: var(--primary-color) !important;
}

[data-theme="dark"] a:hover {
    color: var(--accent-color) !important;
}

[data-theme="dark"] .text-muted {
    color: var(--text-muted) !important;
}

[data-theme="dark"] .border {
    border-color: var(--border-color) !important;
}

[data-theme="dark"] .border-top {
    border-top-color: var(--border-color) !important;
}

[data-theme="dark"] .border-bottom {
    border-bottom-color: var(--border-color) !important;
}

[data-theme="dark"] .border-left {
    border-left-color: var(--border-color) !important;
}

[data-theme="dark"] .border-right {
    border-right-color: var(--border-color) !important;
}

/* Ensure theme wrapper and main containers are properly styled */
[data-theme="dark"] .theme-content-wrapper,
[data-theme="dark"] .site-main,
[data-theme="dark"] main,
body:not([data-theme]) .theme-content-wrapper,
body:not([data-theme]) .site-main,
body:not([data-theme]) main {
    background: var(--background-primary) !important;
    color: var(--text-primary) !important;
}

/* Override any Bootstrap or other framework defaults */
[data-theme="dark"] .bg-white {
    background: var(--card-background, #1a1a1a) !important;
}

[data-theme="dark"] .bg-light {
    background: var(--background-secondary) !important;
}

[data-theme="dark"] .text-dark {
    color: var(--text-primary) !important;
}

[data-theme="dark"] .text-muted {
    color: var(--text-muted) !important;
}

/* Ensure proper styling for reading areas */
[data-theme="dark"] .reading-container,
[data-theme="dark"] .content-wrapper {
    background: var(--background-primary) !important;
    color: var(--text-primary) !important;
}

[data-theme="sepia"] {
    --text-primary: #5c4b37;
    --text-secondary: #8b7355;
    --text-muted: #a08b73;
    --background-primary: #f7f3e9;
    --background-secondary: #f0e6d2;
    --background-tertiary: #e8dcbf;
    --border-color: #d4c4a8;
    --border-light: #e8dcbf;
}

/* Sepia theme button fixes */
[data-theme="sepia"] .btn-primary,
[data-theme="sepia"] .chapter-nav-btn.btn-primary {
    background: #8b6b4f !important;
    border-color: #8b6b4f !important;
    color: #ffffff !important;
}

[data-theme="sepia"] .btn-primary:hover,
[data-theme="sepia"] .chapter-nav-btn.btn-primary:hover {
    background: #6d5439 !important;
    border-color: #6d5439 !important;
    color: #ffffff !important;
}

[data-theme="sepia"] .btn-primary span,
[data-theme="sepia"] .btn-primary i,
[data-theme="sepia"] .chapter-nav-btn.btn-primary span,
[data-theme="sepia"] .chapter-nav-btn.btn-primary i {
    color: #ffffff !important;
}

/* Dark theme outline button fixes */
[data-theme="dark"] .chapter-nav-btn.btn-outline {
    background: transparent !important;
    border: 2px solid var(--primary-color) !important;
    color: var(--primary-color) !important;
}

[data-theme="dark"] .chapter-nav-btn.btn-outline:hover {
    background: var(--primary-color) !important;
    border-color: var(--primary-color) !important;
    color: #ffffff !important;
}

/* Sepia theme outline button fixes */
[data-theme="sepia"] .chapter-nav-btn.btn-outline {
    background: transparent !important;
    border: 2px solid #8b6b4f !important;
    color: #8b6b4f !important;
}

[data-theme="sepia"] .chapter-nav-btn.btn-outline:hover {
    background: #8b6b4f !important;
    border-color: #8b6b4f !important;
    color: #ffffff !important;
}

/* Font size variations */
[data-font-size="small"] .chapter-text {
    font-size: 0.875rem !important;
}

[data-font-size="medium"] .chapter-text {
    font-size: 1rem !important;
}

[data-font-size="large"] .chapter-text {
    font-size: 1.125rem !important;
}

[data-font-size="extra-large"] .chapter-text {
    font-size: 1.25rem !important;
}

/* Lazy loading images */
img.lazy {
    opacity: 0;
    transition: opacity 0.3s;
}

img.lazy.loaded {
    opacity: 1;
}

/* Loading states */
.loading {
    position: relative;
    overflow: hidden;
}

.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.2),
        transparent
    );
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        left: -100%;
    }
    100% {
        left: 100%;
    }
}

/* Enhanced button states */
.btn-action:focus,
.chapter-nav-btn:focus,
.share-btn:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

.btn-action:disabled,
.chapter-nav-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

/* Star rating enhancements */
.star-btn {
    background: none;
    border: none;
    cursor: pointer;
    padding: var(--spacing-xs);
    color: var(--border-color);
    transition: color var(--transition-fast);
}

.star-btn:hover,
.star-btn:focus {
    color: #fbbf24;
    outline: none;
}

.star-btn i.fas {
    color: #fbbf24;
}

.rate-instruction {
    display: block;
    margin-top: var(--spacing-xs);
    color: var(--text-muted);
    font-size: var(--font-size-xs);
}

/* Enhanced responsive design for very small screens */
@media (max-width: 320px) {
    :root {
        --spacing-xs: 0.125rem;
        --spacing-sm: 0.25rem;
        --spacing-md: 0.5rem;
        --spacing-lg: 0.75rem;
        --spacing-xl: 1rem;
        --spacing-2xl: 1.5rem;
    }

    .container {
        padding: 0 var(--spacing-sm);
    }

    .hero-title {
        font-size: var(--font-size-xl);
    }

    .novels-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-sm);
    }

    .genres-grid {
        grid-template-columns: 1fr;
    }

    .chapter-navigation {
        flex-direction: column;
        gap: var(--spacing-sm);
    }

    .nav-controls {
        width: 100%;
        justify-content: space-between;
    }

    .share-buttons {
        flex-direction: column;
        align-items: center;
    }

    .share-btn {
        width: 100%;
        max-width: 200px;
        justify-content: center;
    }
}

/* Performance optimizations */
.chapter-content,
.novel-card,
.chapter-item {
    contain: layout style paint;
}

/* Smooth scrolling for supported browsers */
@supports (scroll-behavior: smooth) {
    html {
        scroll-behavior: smooth;
    }
}

/* Reduced data usage for slow connections */
@media (prefers-reduced-data: reduce) {
    .hero-section::before {
        display: none;
    }

    .novel-cover-main,
    .popular-cover-image {
        filter: none;
        transform: none !important;
    }
}
